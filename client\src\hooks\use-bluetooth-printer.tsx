import { useState, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { bluetoothPrinter } from "@/lib/bluetooth-printer";

export function useBluetoothPrinter() {
  const [isConnected, setIsConnected] = useState(false);
  const [connectedDevice, setConnectedDevice] = useState<BluetoothDevice | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const { toast } = useToast();

  const scanForPrinters = useCallback(async () => {
    try {
      setIsScanning(true);
      const device = await bluetoothPrinter.scanForPrinters();
      
      if (device) {
        setConnectedDevice(device);
        setIsConnected(true);
        toast({
          title: "Printer terhubung",
          description: `Berhasil terhubung dengan ${device.name}`,
        });
      }
    } catch (error) {
      toast({
        title: "Gagal mencari printer",
        description: "Pastikan Bluetooth aktif dan printer dalam mode pairing",
        variant: "destructive",
      });
    } finally {
      setIsScanning(false);
    }
  }, [toast]);

  const connect = useCallback(async (device: BluetoothDevice) => {
    try {
      await bluetoothPrinter.connect(device);
      setConnectedDevice(device);
      setIsConnected(true);
      toast({
        title: "Printer terhubung",
        description: `Berhasil terhubung dengan ${device.name}`,
      });
    } catch (error) {
      toast({
        title: "Gagal terhubung",
        description: "Tidak dapat terhubung dengan printer",
        variant: "destructive",
      });
    }
  }, [toast]);

  const disconnect = useCallback(async () => {
    try {
      await bluetoothPrinter.disconnect();
      setIsConnected(false);
      setConnectedDevice(null);
      toast({
        title: "Printer terputus",
        description: "Koneksi printer telah diputus",
      });
    } catch (error) {
      toast({
        title: "Gagal memutus koneksi",
        description: "Terjadi kesalahan saat memutus koneksi",
        variant: "destructive",
      });
    }
  }, [toast]);

  const print = useCallback(async (data: string) => {
    try {
      await bluetoothPrinter.print(data);
      toast({
        title: "Berhasil mencetak",
        description: "Struk berhasil dicetak",
      });
    } catch (error) {
      toast({
        title: "Gagal mencetak",
        description: "Terjadi kesalahan saat mencetak",
        variant: "destructive",
      });
      throw error;
    }
  }, [toast]);

  return {
    isConnected,
    connectedDevice,
    isScanning,
    scanForPrinters,
    connect,
    disconnect,
    print,
  };
}
