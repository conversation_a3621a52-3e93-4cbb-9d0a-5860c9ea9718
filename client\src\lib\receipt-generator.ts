import { formatCurrency } from "./currency";

interface ReceiptData {
  transaction: {
    id: number;
    subtotal: number;
    tax: number;
    total: number;
    paymentAmount: number;
    change: number;
    createdAt: Date;
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  settings: {
    storeName: string;
    storeAddress: string;
    storePhone: string;
    paperSize: string;
  };
}

export function generateReceipt(data: ReceiptData): string {
  const { transaction, items, settings } = data;
  const date = new Date(transaction.createdAt);
  const formattedDate = date.toLocaleDateString('id-ID');
  const formattedTime = date.toLocaleTimeString('id-ID', { 
    hour: '2-digit', 
    minute: '2-digit',
    timeZone: 'Asia/Jakarta'
  });

  const width = settings.paperSize === "58mm" ? 32 : 48;
  const separator = "=".repeat(width);
  const line = "-".repeat(width);

  let receipt = "";
  
  // Header
  receipt += centerText(settings.storeName, width) + "\n";
  receipt += centerText(settings.storeAddress, width) + "\n";
  receipt += centerText(`Tel: ${settings.storePhone}`, width) + "\n";
  receipt += separator + "\n";
  
  // Transaction info
  receipt += centerText(`#${transaction.id.toString().padStart(3, '0')}`, width) + "\n";
  receipt += centerText(`${formattedDate} ${formattedTime} WIB`, width) + "\n";
  receipt += separator + "\n";
  
  // Items
  items.forEach(item => {
    const itemLine = `${item.name}`;
    const qtyPrice = `${item.quantity}x${formatCurrency(item.price)}`;
    const total = formatCurrency(item.price * item.quantity);
    
    receipt += itemLine + "\n";
    receipt += justifyText(qtyPrice, total, width) + "\n";
  });
  
  receipt += line + "\n";
  
  // Totals
  receipt += justifyText("Subtotal:", formatCurrency(transaction.subtotal), width) + "\n";
  
  if (transaction.tax > 0) {
    receipt += justifyText("Pajak:", formatCurrency(transaction.tax), width) + "\n";
  }
  
  receipt += justifyText("Total:", formatCurrency(transaction.total), width) + "\n";
  receipt += justifyText("Bayar:", formatCurrency(transaction.paymentAmount), width) + "\n";
  receipt += justifyText("Kembalian:", formatCurrency(transaction.change), width) + "\n";
  
  receipt += separator + "\n";
  
  // Footer
  receipt += centerText("Terima kasih atas kunjungan Anda!", width) + "\n";
  receipt += centerText("Barang yang sudah dibeli", width) + "\n";
  receipt += centerText("tidak dapat dikembalikan", width) + "\n";
  
  return receipt;
}

function centerText(text: string, width: number): string {
  const padding = Math.max(0, Math.floor((width - text.length) / 2));
  return " ".repeat(padding) + text;
}

function justifyText(left: string, right: string, width: number): string {
  const spaces = Math.max(1, width - left.length - right.length);
  return left + " ".repeat(spaces) + right;
}
