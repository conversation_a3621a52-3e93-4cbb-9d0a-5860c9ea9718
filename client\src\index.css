@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 98%);
  --foreground: hsl(0, 0%, 13%);
  --muted: hsl(120, 20%, 96%);
  --muted-foreground: hsl(0, 0%, 40%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0, 0%, 13%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(0, 0%, 13%);
  --border: hsl(120, 10%, 90%);
  --input: hsl(120, 10%, 90%);
  --primary: hsl(122, 39%, 49%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(120, 20%, 96%);
  --secondary-foreground: hsl(0, 0%, 13%);
  --accent: hsl(120, 20%, 96%);
  --accent-foreground: hsl(0, 0%, 13%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --ring: hsl(122, 39%, 49%);
  --radius: 0.5rem;
  --surface: hsl(0, 0%, 100%);
  --surface-dark: hsl(0, 0%, 12%);
  --card-dark: hsl(0, 0%, 20%);
  --text: hsl(0, 0%, 13%);
  --text-dark: hsl(0, 0%, 100%);
  --text-secondary: hsl(0, 0%, 40%);
  --text-secondary-dark: hsl(0, 0%, 80%);
  --primary-dark: hsl(122, 39%, 34%);
}

.dark {
  --background: hsl(0, 0%, 6%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(0, 0%, 15%);
  --muted-foreground: hsl(0, 0%, 65%);
  --popover: hsl(0, 0%, 6%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(0, 0%, 6%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(0, 0%, 15%);
  --input: hsl(0, 0%, 15%);
  --primary: hsl(122, 39%, 49%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(0, 0%, 15%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(0, 0%, 15%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62%, 30%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(0, 0%, 84%);
  --radius: 0.5rem;
  --surface: hsl(0, 0%, 12%);
  --surface-dark: hsl(0, 0%, 12%);
  --card-dark: hsl(0, 0%, 20%);
  --text: hsl(0, 0%, 13%);
  --text-dark: hsl(0, 0%, 100%);
  --text-secondary: hsl(0, 0%, 40%);
  --text-secondary-dark: hsl(0, 0%, 80%);
  --primary-dark: hsl(122, 39%, 34%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

@layer components {
  .safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .ripple-effect {
    @apply relative overflow-hidden;
  }

  .ripple-effect::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }

  .ripple-effect:active::before {
    width: 200px;
    height: 200px;
  }
}

/* Custom colors for POS system */
.text-surface {
  color: var(--surface);
}

.bg-surface {
  background-color: var(--surface);
}

.dark .bg-surface {
  background-color: var(--surface-dark);
}

.text-surface-dark {
  color: var(--surface-dark);
}

.bg-surface-dark {
  background-color: var(--surface-dark);
}

.text-card-dark {
  color: var(--card-dark);
}

.bg-card-dark {
  background-color: var(--card-dark);
}

.text-text-dark {
  color: var(--text-dark);
}

.text-text-secondary-dark {
  color: var(--text-secondary-dark);
}

.bg-primary-dark {
  background-color: var(--primary-dark);
}

.text-primary-dark {
  color: var(--primary-dark);
}
