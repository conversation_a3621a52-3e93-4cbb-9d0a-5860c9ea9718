import { useState } from "react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { X, CreditCard, Banknote, Printer } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useBluetoothPrinter } from "@/hooks/use-bluetooth-printer";
import { apiRequest } from "@/lib/queryClient";
import { formatCurrency } from "@/lib/currency";
import { generateReceipt } from "@/lib/receipt-generator";
import type { Setting } from "@shared/schema";

interface PaymentModalProps {
  open: boolean;
  onClose: () => void;
  transaction: Array<{
    productId: number;
    name: string;
    price: number;
    quantity: number;
  }>;
  onPaymentComplete: () => void;
}

export default function PaymentModal({ open, onClose, transaction, onPaymentComplete }: PaymentModalProps) {
  const [paymentAmount, setPaymentAmount] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { print } = useBluetoothPrinter();

  const { data: settings = [] } = useQuery({
    queryKey: ["/api/settings"],
    staleTime: 5 * 60 * 1000,
  });

  const getSetting = (key: string, defaultValue: string = "") => {
    const setting = settings.find((s: Setting) => s.key === key);
    return setting?.value || defaultValue;
  };

  const taxEnabled = getSetting("taxEnabled") === "true";
  const taxRate = parseFloat(getSetting("taxRate", "11"));
  const autoPrint = getSetting("autoPrint") === "true";

  const subtotal = transaction.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = taxEnabled ? Math.round(subtotal * (taxRate / 100)) : 0;
  const total = subtotal + tax;
  const change = Math.max(0, parseInt(paymentAmount) - total);

  const processPaymentMutation = useMutation({
    mutationFn: async () => {
      const transactionData = {
        items: transaction.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
        })),
        subtotal,
        tax,
        total,
        paymentAmount: parseInt(paymentAmount),
        change,
      };

      const response = await apiRequest("POST", "/api/transactions", transactionData);
      return response.json();
    },
    onSuccess: async (newTransaction) => {
      queryClient.invalidateQueries({ queryKey: ["/api/transactions"] });
      
      toast({
        title: "Pembayaran berhasil",
        description: "Transaksi telah disimpan",
      });
      
      onPaymentComplete();
      onClose();
      setPaymentAmount("");
    },
    onError: () => {
      toast({
        title: "Pembayaran gagal",
        description: "Terjadi kesalahan saat memproses pembayaran",
        variant: "destructive",
      });
    },
  });

  const printReceiptMutation = useMutation({
    mutationFn: async (transactionData: any) => {
      const response = await apiRequest("POST", "/api/transactions", transactionData);
      const newTransaction = await response.json();
      
      // Generate and print receipt
      const receipt = generateReceipt({
        transaction: newTransaction,
        items: transaction,
        settings: {
          storeName: getSetting("storeName", "Warung Kopi Pak Budi"),
          storeAddress: getSetting("storeAddress", "Jl. Merdeka No. 123, Jakarta"),
          storePhone: getSetting("storePhone", "021-12345678"),
          paperSize: getSetting("paperSize", "58mm"),
        },
      });
      
      await print(receipt);
      return newTransaction;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/transactions"] });
      
      toast({
        title: "Pembayaran berhasil",
        description: "Struk sedang dicetak",
      });
      
      onPaymentComplete();
      onClose();
      setPaymentAmount("");
    },
    onError: () => {
      toast({
        title: "Gagal memproses",
        description: "Pastikan printer Bluetooth terhubung",
        variant: "destructive",
      });
    },
  });

  const handlePayment = () => {
    if (!paymentAmount || parseInt(paymentAmount) < total) {
      toast({
        title: "Jumlah pembayaran tidak valid",
        description: "Jumlah pembayaran harus lebih dari atau sama dengan total",
        variant: "destructive",
      });
      return;
    }
    
    processPaymentMutation.mutate();
  };

  const handlePaymentAndPrint = () => {
    if (!paymentAmount || parseInt(paymentAmount) < total) {
      toast({
        title: "Jumlah pembayaran tidak valid",
        description: "Jumlah pembayaran harus lebih dari atau sama dengan total",
        variant: "destructive",
      });
      return;
    }

    const transactionData = {
      items: transaction.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
      })),
      subtotal,
      tax,
      total,
      paymentAmount: parseInt(paymentAmount),
      change,
    };
    
    printReceiptMutation.mutate(transactionData);
  };

  const quickAmounts = [50000, 100000, 200000];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Pembayaran</span>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                {taxEnabled && (
                  <div className="flex justify-between text-sm">
                    <span>Pajak ({taxRate}%)</span>
                    <span>{formatCurrency(tax)}</span>
                  </div>
                )}
                <div className="border-t pt-2">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span className="text-primary">{formatCurrency(total)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <div>
            <Label className="flex items-center space-x-2 mb-2">
              <Banknote className="h-4 w-4" />
              <span>Jumlah Bayar</span>
            </Label>
            <Input
              type="number"
              value={paymentAmount}
              onChange={(e) => setPaymentAmount(e.target.value)}
              placeholder="0"
              className="text-lg"
            />
          </div>
          
          <div className="grid grid-cols-3 gap-2">
            {quickAmounts.map((amount) => (
              <Button
                key={amount}
                variant="outline"
                onClick={() => setPaymentAmount(amount.toString())}
                className="text-sm"
              >
                {formatCurrency(amount)}
              </Button>
            ))}
            <Button
              variant="outline"
              onClick={() => setPaymentAmount(total.toString())}
              className="text-sm"
            >
              Pas
            </Button>
          </div>
          
          {paymentAmount && parseInt(paymentAmount) >= total && (
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <span className="text-sm text-gray-500">Kembalian</span>
                  <p className="text-2xl font-bold text-primary">{formatCurrency(change)}</p>
                </div>
              </CardContent>
            </Card>
          )}
          
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Batal
            </Button>
            <Button
              onClick={handlePayment}
              disabled={!paymentAmount || parseInt(paymentAmount) < total || processPaymentMutation.isPending}
              className="flex-1"
              variant="outline"
            >
              {processPaymentMutation.isPending ? "Memproses..." : "Bayar"}
            </Button>
            <Button
              onClick={handlePaymentAndPrint}
              disabled={!paymentAmount || parseInt(paymentAmount) < total || printReceiptMutation.isPending}
              className="flex-1"
            >
              <Printer className="h-4 w-4 mr-2" />
              {printReceiptMutation.isPending ? "Memproses..." : "Cetak"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
