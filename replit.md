# Point of Sale (POS) System

## Overview

This is a full-stack Point of Sale system built with React, TypeScript, Express, and PostgreSQL. It's designed as a mobile-first PWA (Progressive Web App) for Indonesian businesses, featuring Bluetooth thermal printer support, offline capabilities, and a complete inventory management system.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Library**: Shadcn/ui components built on Radix UI primitives
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **State Management**: React Query (TanStack Query) for server state
- **Routing**: Wouter for lightweight client-side routing
- **Forms**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM
- **Validation**: Zod schemas shared between client and server
- **Development**: Hot reload with Vite integration in development

### Mobile & PWA Features
- **Progressive Web App**: Service worker for offline functionality
- **Responsive Design**: Mobile-first approach with bottom navigation
- **Bluetooth Printing**: Web Bluetooth API for thermal receipt printers
- **Offline Support**: Local storage and caching for offline operations

## Key Components

### Database Schema (shared/schema.ts)
- **Products**: Basic product information with stock tracking
- **Transactions**: Complete transaction records with line items
- **Settings**: Key-value store for application configuration
- Uses Drizzle ORM with PostgreSQL dialect

### API Layer (server/routes.ts)
- RESTful API endpoints for all CRUD operations
- Products: GET, POST, PUT, DELETE with validation
- Transactions: GET, POST for sales processing
- Settings: GET, PUT for configuration management
- Zod schema validation on all inputs

### Frontend Components
- **Tabbed Interface**: Sales, Inventory, Reports, Settings
- **Transaction Processing**: Cart management and payment flow
- **Bluetooth Printing**: Receipt generation and printer connectivity
- **Dark Mode**: Theme switching with persistence

### Storage Layer
- **Production**: PostgreSQL with Drizzle ORM
- **Development**: In-memory storage with sample data
- **Interface**: IStorage abstraction for easy testing/mocking

## Data Flow

### Transaction Flow
1. User selects products in Sales tab
2. Products added to local transaction state
3. Payment modal processes payment calculation
4. Transaction submitted to API with validation
5. Receipt generated and optionally printed via Bluetooth
6. Transaction stored in database
7. Stock levels updated automatically

### Bluetooth Printing Flow
1. User scans for available Bluetooth printers
2. Connection established via Web Bluetooth API
3. Receipt data converted to ESC/POS commands
4. Commands sent to printer via GATT characteristic
5. Connection maintained for subsequent prints

### Offline Support
1. Service worker caches static assets
2. Local storage preserves transaction state
3. Settings cached for offline access
4. Graceful degradation when API unavailable

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL database connectivity
- **drizzle-orm**: Type-safe database operations
- **@tanstack/react-query**: Server state management
- **@radix-ui/***: Headless UI components
- **react-hook-form**: Form state management
- **zod**: Schema validation

### Development Dependencies
- **drizzle-kit**: Database schema management
- **tsx**: TypeScript execution
- **vite**: Build tool and dev server
- **tailwindcss**: Utility-first CSS framework

### Hardware Integration
- **Web Bluetooth API**: For thermal printer connectivity
- **ESC/POS Commands**: Printer control protocol
- **Service Worker**: PWA offline functionality

## Deployment Strategy

### Build Process
1. **Frontend**: Vite builds React app to `dist/public`
2. **Backend**: esbuild bundles Node.js server to `dist/index.js`
3. **Database**: Drizzle pushes schema changes to PostgreSQL
4. **Assets**: Static files served from build directory

### Environment Configuration
- **DATABASE_URL**: PostgreSQL connection string (required)
- **NODE_ENV**: Environment mode (development/production)
- **REPL_ID**: Replit-specific configuration

### Development Workflow
- `npm run dev`: Starts development server with hot reload
- `npm run db:push`: Applies database schema changes
- `npm run build`: Creates production build
- `npm run start`: Runs production server

### Database Management
- **Migrations**: Stored in `./migrations` directory
- **Schema**: Centralized in `shared/schema.ts`
- **Connection**: Configured via environment variables
- **ORM**: Drizzle provides type-safe database operations

The system is designed to be easily deployable on platforms like Replit, Vercel, or any Node.js hosting service with PostgreSQL support.