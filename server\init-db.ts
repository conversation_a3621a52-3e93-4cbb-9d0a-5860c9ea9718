import { db } from "./db";
import { products, settings } from "@shared/schema";

export async function initializeDatabase() {
  try {
    // Check if we already have sample data
    const existingProducts = await db.select().from(products).limit(1);
    const existingSettings = await db.select().from(settings).limit(1);
    
    if (existingProducts.length === 0) {
      // Add sample products
      await db.insert(products).values([
        { name: "Kopi <PERSON>", price: 15000, stock: 25, category: "Minuman", barcode: null },
        { name: "<PERSON><PERSON>", price: 10000, stock: 18, category: "Minuman", barcode: null },
        { name: "Nasi <PERSON>", price: 25000, stock: 12, category: "Makanan", barcode: null },
        { name: "<PERSON><PERSON>", price: 20000, stock: 8, category: "Makanan", barcode: null },
      ]);
      console.log("Sample products added to database");
    }

    if (existingSettings.length === 0) {
      // Initialize default settings
      await db.insert(settings).values([
        { key: "darkMode", value: "false" },
        { key: "taxEnabled", value: "true" },
        { key: "taxRate", value: "11" },
        { key: "storeName", value: "Warung Kopi Pak Budi" },
        { key: "storeAddress", value: "Jl. Merdeka No. 123, Jakarta" },
        { key: "storePhone", value: "021-12345678" },
        { key: "autoPrint", value: "true" },
        { key: "paperSize", value: "58mm" },
        { key: "showAds", value: "false" },
      ]);
      console.log("Default settings added to database");
    }
  } catch (error) {
    console.error("Database initialization error:", error);
  }
}