import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, Sun, Bluetooth, Printer, Play } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { useTheme } from "@/hooks/use-theme";
import { useBluetoothPrinter } from "@/hooks/use-bluetooth-printer";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { Setting } from "@shared/schema";

export default function SettingsTab() {
  const { theme, toggleTheme } = useTheme();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isConnected, connectedDevice, scanForPrinters, connect, disconnect } = useBluetoothPrinter();

  const { data: settings = [], isLoading } = useQuery({
    queryKey: ["/api/settings"],
    staleTime: 5 * 60 * 1000,
  });

  const updateSettingMutation = useMutation({
    mutationFn: async ({ key, value }: { key: string; value: string }) => {
      const response = await apiRequest("PUT", `/api/settings/${key}`, { value });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/settings"] });
      toast({
        title: "Pengaturan disimpan",
        description: "Pengaturan berhasil diperbarui",
      });
    },
    onError: () => {
      toast({
        title: "Gagal menyimpan",
        description: "Terjadi kesalahan saat menyimpan pengaturan",
        variant: "destructive",
      });
    },
  });

  const getSetting = (key: string, defaultValue: string = "") => {
    const setting = settings.find((s: Setting) => s.key === key);
    return setting?.value || defaultValue;
  };

  const updateSetting = (key: string, value: string) => {
    updateSettingMutation.mutate({ key, value });
  };

  if (isLoading) {
    return (
      <div className="p-4 space-y-4">
        <Skeleton className="h-8 w-32" />
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-32 w-full" />
        ))}
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">Pengaturan</h2>
      
      <div className="space-y-4">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Umum</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                {theme === "dark" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                <div>
                  <Label className="font-medium">Mode Gelap</Label>
                  <p className="text-sm text-gray-500">Aktifkan tampilan gelap</p>
                </div>
              </div>
              <Switch
                checked={theme === "dark"}
                onCheckedChange={toggleTheme}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <div>
                <Label className="font-medium">Pajak (PPN)</Label>
                <p className="text-sm text-gray-500">Aktifkan perhitungan pajak</p>
              </div>
              <Switch
                checked={getSetting("taxEnabled") === "true"}
                onCheckedChange={(checked) => updateSetting("taxEnabled", checked.toString())}
              />
            </div>
            
            <div className="flex justify-between items-center">
              <div>
                <Label className="font-medium">Persentase Pajak</Label>
                <p className="text-sm text-gray-500">Atur persentase PPN</p>
              </div>
              <div className="w-20">
                <Input
                  type="number"
                  value={getSetting("taxRate", "11")}
                  onChange={(e) => updateSetting("taxRate", e.target.value)}
                  min="0"
                  max="100"
                  className="text-center"
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Printer Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Printer className="h-5 w-5" />
              <span>Printer</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <Bluetooth className="h-4 w-4" />
                <div>
                  <Label className="font-medium">Printer Bluetooth</Label>
                  <p className="text-sm text-gray-500">
                    {isConnected ? `Terhubung: ${connectedDevice?.name}` : "Belum terhubung"}
                  </p>
                </div>
              </div>
              <div className="space-x-2">
                {!isConnected ? (
                  <Button onClick={scanForPrinters} className="text-sm">
                    Cari Printer
                  </Button>
                ) : (
                  <Button onClick={disconnect} variant="outline" className="text-sm">
                    Putuskan
                  </Button>
                )}
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <div>
                <Label className="font-medium">Ukuran Kertas</Label>
                <p className="text-sm text-gray-500">Pilih ukuran kertas thermal</p>
              </div>
              <Select
                value={getSetting("paperSize", "58mm")}
                onValueChange={(value) => updateSetting("paperSize", value)}
              >
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="58mm">58mm</SelectItem>
                  <SelectItem value="80mm">80mm</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex justify-between items-center">
              <div>
                <Label className="font-medium">Auto Print</Label>
                <p className="text-sm text-gray-500">Cetak otomatis setelah transaksi</p>
              </div>
              <Switch
                checked={getSetting("autoPrint") === "true"}
                onCheckedChange={(checked) => updateSetting("autoPrint", checked.toString())}
              />
            </div>
          </CardContent>
        </Card>
        
        {/* Business Info */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Bisnis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Nama Toko</Label>
              <Input
                value={getSetting("storeName", "Warung Kopi Pak Budi")}
                onChange={(e) => updateSetting("storeName", e.target.value)}
                placeholder="Nama toko"
              />
            </div>
            
            <div>
              <Label>Alamat</Label>
              <Textarea
                value={getSetting("storeAddress", "Jl. Merdeka No. 123, Jakarta")}
                onChange={(e) => updateSetting("storeAddress", e.target.value)}
                placeholder="Alamat toko"
                rows={2}
              />
            </div>
            
            <div>
              <Label>Telepon</Label>
              <Input
                value={getSetting("storePhone", "021-12345678")}
                onChange={(e) => updateSetting("storePhone", e.target.value)}
                placeholder="Nomor telepon"
              />
            </div>
          </CardContent>
        </Card>
        
        {/* Optional Ads */}
        <Card>
          <CardHeader>
            <CardTitle>Iklan (Opsional)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div>
                <Label className="font-medium">Lihat Iklan</Label>
                <p className="text-sm text-gray-500">Dukung developer dengan melihat iklan</p>
              </div>
              <Button 
                onClick={() => {
                  // Open ads in new window/tab
                  window.open('https://example.com/ads', '_blank');
                  toast({
                    title: "Terima kasih!",
                    description: "Dukungan Anda sangat berarti untuk pengembangan aplikasi ini",
                  });
                }}
                className="flex items-center space-x-2"
              >
                <Play className="h-4 w-4" />
                <span>Tonton Iklan</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
