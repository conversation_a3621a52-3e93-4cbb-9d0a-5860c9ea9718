import { generateESCPOSCommands } from "./escpos-commands";

class BluetoothPrinter {
  private device: BluetoothDevice | null = null;
  private server: BluetoothRemoteGATTServer | null = null;
  private service: BluetoothRemoteGATTService | null = null;
  private characteristic: BluetoothRemoteGATTCharacteristic | null = null;

  async scanForPrinters(): Promise<BluetoothDevice> {
    if (!navigator.bluetooth) {
      throw new Error("Bluetooth tidak didukung di browser ini");
    }

    try {
      const device = await navigator.bluetooth.requestDevice({
        acceptAllDevices: true,
        optionalServices: [
          "000018f0-0000-1000-8000-00805f9b34fb", // Common thermal printer service
          "0000ff00-0000-1000-8000-00805f9b34fb", // Alternative service
        ],
      });

      await this.connect(device);
      return device;
    } catch (error) {
      console.error("Bluetooth scan error:", error);
      throw error;
    }
  }

  async connect(device: BluetoothDevice): Promise<void> {
    try {
      this.device = device;
      this.server = await device.gatt!.connect();
      
      // Try to find a suitable service
      const services = await this.server.getPrimaryServices();
      
      for (const service of services) {
        try {
          const characteristics = await service.getCharacteristics();
          
          for (const characteristic of characteristics) {
            if (characteristic.properties.write || characteristic.properties.writeWithoutResponse) {
              this.service = service;
              this.characteristic = characteristic;
              return;
            }
          }
        } catch (error) {
          console.warn("Error checking service characteristics:", error);
        }
      }
      
      throw new Error("No suitable characteristic found for printing");
    } catch (error) {
      console.error("Bluetooth connection error:", error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.server) {
      this.server.disconnect();
      this.server = null;
      this.service = null;
      this.characteristic = null;
      this.device = null;
    }
  }

  async print(data: string): Promise<void> {
    if (!this.characteristic) {
      throw new Error("Tidak terhubung dengan printer");
    }

    try {
      const commands = generateESCPOSCommands(data);
      
      // Send data in chunks to avoid buffer overflow
      const chunkSize = 20;
      for (let i = 0; i < commands.length; i += chunkSize) {
        const chunk = commands.slice(i, i + chunkSize);
        await this.characteristic.writeValue(chunk);
        
        // Small delay between chunks
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    } catch (error) {
      console.error("Print error:", error);
      throw error;
    }
  }

  isConnected(): boolean {
    return this.device !== null && this.server !== null && this.server.connected;
  }
}

export const bluetoothPrinter = new BluetoothPrinter();
