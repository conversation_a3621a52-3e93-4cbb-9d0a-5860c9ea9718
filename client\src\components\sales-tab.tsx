import { useState } from "react";
import { Search, ShoppingCart, Minus, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/currency";
import type { Product } from "@shared/schema";

interface SalesTabProps {
  products: Product[];
  isLoading: boolean;
  currentTransaction: Array<{
    productId: number;
    name: string;
    price: number;
    quantity: number;
  }>;
  addToTransaction: (product: Product) => void;
  removeFromTransaction: (productId: number) => void;
  updateQuantity: (productId: number, quantity: number) => void;
  onPayment: () => void;
}

export default function SalesTab({
  products,
  isLoading,
  currentTransaction,
  addToTransaction,
  removeFromTransaction,
  updateQuantity,
  onPayment,
}: SalesTabProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalItems = currentTransaction.reduce((sum, item) => sum + item.quantity, 0);
  const subtotal = currentTransaction.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  if (isLoading) {
    return (
      <div className="p-4 space-y-4">
        <Skeleton className="h-12 w-full" />
        <div className="grid grid-cols-2 gap-3">
          {Array.from({ length: 8 }).map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Product Search */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Cari produk..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Product Grid */}
      <div className="p-4">
        <div className="grid grid-cols-2 gap-3">
          {filteredProducts.map((product) => (
            <Card
              key={product.id}
              className="cursor-pointer hover:shadow-md transition-shadow ripple-effect"
              onClick={() => addToTransaction(product)}
            >
              <CardContent className="p-4">
                <h3 className="font-medium text-sm mb-1">{product.name}</h3>
                <p className="text-primary font-semibold text-lg">
                  {formatCurrency(product.price)}
                </p>
                <div className="flex items-center justify-between mt-2">
                  <p className="text-xs text-gray-500">
                    Stok: {product.stock}
                  </p>
                  {product.stock <= 5 && (
                    <Badge variant="destructive" className="text-xs">
                      Stok rendah
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Current Transaction */}
      {currentTransaction.length > 0 && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 w-full max-w-sm px-4">
          <Card className="shadow-lg">
            <CardContent className="p-4">
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-semibold">Transaksi Saat Ini</h3>
                <div className="flex items-center space-x-2">
                  <ShoppingCart className="h-4 w-4" />
                  <span className="text-sm text-gray-500">{totalItems} item</span>
                </div>
              </div>
              
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {currentTransaction.map((item) => (
                  <div key={item.productId} className="flex justify-between items-center text-sm">
                    <div className="flex-1">
                      <span>{item.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          updateQuantity(item.productId, item.quantity - 1);
                        }}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                      <span className="w-8 text-center">{item.quantity}</span>
                      <Button
                        variant="outline"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation();
                          updateQuantity(item.productId, item.quantity + 1);
                        }}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                      <span className="w-20 text-right">
                        {formatCurrency(item.price * item.quantity)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span className="text-primary">{formatCurrency(subtotal)}</span>
                </div>
              </div>
              
              <Button
                className="w-full mt-3 ripple-effect"
                onClick={onPayment}
                disabled={currentTransaction.length === 0}
              >
                Proses Pembayaran
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
