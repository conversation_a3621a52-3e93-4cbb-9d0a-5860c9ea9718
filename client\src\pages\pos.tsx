import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Moon, Wifi } from "lucide-react";
import { useTheme } from "@/hooks/use-theme";
import { useLocalStorage } from "@/hooks/use-local-storage";
import BottomNavigation from "@/components/bottom-navigation";
import SalesTab from "@/components/sales-tab";
import InventoryTab from "@/components/inventory-tab";
import ReportsTab from "@/components/reports-tab";
import SettingsTab from "@/components/settings-tab";
import PaymentModal from "@/components/payment-modal";
import { Button } from "@/components/ui/button";
import type { Product } from "@shared/schema";

export default function POSSystem() {
  const { theme, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useLocalStorage("activeTab", "sales");
  const [currentTransaction, setCurrentTransaction] = useLocalStorage("currentTransaction", []);
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);

  const { data: products = [], isLoading } = useQuery({
    queryKey: ["/api/products"],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // PWA installation
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/service-worker.js');
    }
  }, []);

  const addToTransaction = (product: Product) => {
    setCurrentTransaction(prev => {
      const existingItem = prev.find(item => item.productId === product.id);
      if (existingItem) {
        return prev.map(item =>
          item.productId === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      return [...prev, { productId: product.id, name: product.name, price: product.price, quantity: 1 }];
    });
  };

  const removeFromTransaction = (productId: number) => {
    setCurrentTransaction(prev => prev.filter(item => item.productId !== productId));
  };

  const updateQuantity = (productId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromTransaction(productId);
      return;
    }
    setCurrentTransaction(prev =>
      prev.map(item =>
        item.productId === productId ? { ...item, quantity } : item
      )
    );
  };

  const clearTransaction = () => {
    setCurrentTransaction([]);
  };

  const renderTab = () => {
    switch (activeTab) {
      case "sales":
        return (
          <SalesTab
            products={products}
            isLoading={isLoading}
            currentTransaction={currentTransaction}
            addToTransaction={addToTransaction}
            removeFromTransaction={removeFromTransaction}
            updateQuantity={updateQuantity}
            onPayment={() => setPaymentModalOpen(true)}
          />
        );
      case "inventory":
        return <InventoryTab products={products} isLoading={isLoading} />;
      case "reports":
        return <ReportsTab />;
      case "settings":
        return <SettingsTab />;
      default:
        return <SalesTab products={products} isLoading={isLoading} currentTransaction={currentTransaction} addToTransaction={addToTransaction} removeFromTransaction={removeFromTransaction} updateQuantity={updateQuantity} onPayment={() => setPaymentModalOpen(true)} />;
    }
  };

  return (
    <div className="max-w-sm mx-auto bg-surface dark:bg-surface-dark min-h-screen relative">
      {/* Header */}
      <header className="bg-primary text-white p-4 shadow-lg">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold">Sistem Kasir</h1>
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="text-white hover:bg-primary-dark"
            >
              <Moon className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-primary-dark"
            >
              <Wifi className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="pb-20 safe-area">
        {renderTab()}
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation activeTab={activeTab} setActiveTab={setActiveTab} />

      {/* Payment Modal */}
      <PaymentModal
        open={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        transaction={currentTransaction}
        onPaymentComplete={clearTransaction}
      />
    </div>
  );
}
