import { useQuery } from "@tanstack/react-query";
import { Printer } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useBluetoothPrinter } from "@/hooks/use-bluetooth-printer";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/currency";
import { generateReceipt } from "@/lib/receipt-generator";
import type { Transaction, Setting } from "@shared/schema";

export default function ReportsTab() {
  const { toast } = useToast();
  const { print } = useBluetoothPrinter();
  
  const { data: transactions = [], isLoading } = useQuery({
    queryKey: ["/api/transactions"],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: settings = [] } = useQuery({
    queryKey: ["/api/settings"],
    staleTime: 5 * 60 * 1000,
  });

  const getSetting = (key: string, defaultValue: string = "") => {
    const setting = settings.find((s: Setting) => s.key === key);
    return setting?.value || defaultValue;
  };

  const today = new Date();
  const todayTransactions = transactions.filter(
    (t: Transaction) => 
      new Date(t.createdAt!).toDateString() === today.toDateString()
  );

  const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const monthTransactions = transactions.filter(
    (t: Transaction) => new Date(t.createdAt!) >= thisMonth
  );

  const todayTotal = todayTransactions.reduce((sum: number, t: Transaction) => sum + t.total, 0);
  const monthTotal = monthTransactions.reduce((sum: number, t: Transaction) => sum + t.total, 0);
  const todayItemCount = todayTransactions.reduce((sum: number, t: Transaction) => {
    const items = Array.isArray(t.items) ? t.items : [];
    return sum + items.reduce((itemSum: number, item: any) => itemSum + (item.quantity || 0), 0);
  }, 0);

  const recentTransactions = transactions.slice(0, 10);

  const handlePrintTransaction = async (transaction: Transaction) => {
    try {
      const items = Array.isArray(transaction.items) ? transaction.items : [];
      const receipt = generateReceipt({
        transaction,
        items,
        settings: {
          storeName: getSetting("storeName", "Warung Kopi Pak Budi"),
          storeAddress: getSetting("storeAddress", "Jl. Merdeka No. 123, Jakarta"),
          storePhone: getSetting("storePhone", "021-12345678"),
          paperSize: getSetting("paperSize", "58mm"),
        },
      });
      
      await print(receipt);
      toast({
        title: "Mencetak struk",
        description: `Struk transaksi #TRX-${transaction.id.toString().padStart(3, '0')} sedang dicetak`,
      });
    } catch (error) {
      toast({
        title: "Gagal mencetak",
        description: "Pastikan printer Bluetooth terhubung",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-2 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">Laporan Penjualan</h2>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm text-gray-500 mb-1">Hari Ini</h3>
            <p className="text-2xl font-bold text-primary">{formatCurrency(todayTotal)}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm text-gray-500 mb-1">Bulan Ini</h3>
            <p className="text-2xl font-bold text-primary">{formatCurrency(monthTotal)}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm text-gray-500 mb-1">Transaksi Hari Ini</h3>
            <p className="text-2xl font-bold text-primary">{todayTransactions.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm text-gray-500 mb-1">Produk Terjual</h3>
            <p className="text-2xl font-bold text-primary">{todayItemCount}</p>
          </CardContent>
        </Card>
      </div>
      
      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Transaksi Terbaru</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentTransactions.map((transaction: Transaction) => {
              const items = Array.isArray(transaction.items) ? transaction.items : [];
              const itemCount = items.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0);
              const date = new Date(transaction.createdAt!);
              
              return (
                <div key={transaction.id} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-sm">#TRX-{transaction.id.toString().padStart(3, '0')}</p>
                    <p className="text-xs text-gray-500">
                      {date.toLocaleTimeString('id-ID', { 
                        hour: '2-digit', 
                        minute: '2-digit',
                        timeZone: 'Asia/Jakarta'
                      })} WIB
                    </p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <p className="font-semibold text-primary">{formatCurrency(transaction.total)}</p>
                      <p className="text-xs text-gray-500">{itemCount} item</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handlePrintTransaction(transaction)}
                      className="text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900"
                    >
                      <Printer className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
            
            {recentTransactions.length === 0 && (
              <p className="text-center text-gray-500 py-8">Belum ada transaksi</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
