import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/currency";

interface ReceiptPreviewProps {
  transaction: {
    id: number;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
    }>;
    subtotal: number;
    tax: number;
    total: number;
    paymentAmount: number;
    change: number;
    createdAt: Date;
  };
  settings: {
    storeName: string;
    storeAddress: string;
    storePhone: string;
    paperSize: string;
  };
}

export default function ReceiptPreview({ transaction, settings }: ReceiptPreviewProps) {
  const date = new Date(transaction.createdAt);
  const formattedDate = date.toLocaleDateString('id-ID');
  const formattedTime = date.toLocaleTimeString('id-ID', { 
    hour: '2-digit', 
    minute: '2-digit',
    timeZone: 'Asia/Jakarta'
  });

  return (
    <Card className="max-w-xs mx-auto bg-white text-black font-mono text-sm">
      <CardContent className="p-4 space-y-2">
        {/* Header */}
        <div className="text-center border-b pb-2">
          <div className="font-bold text-lg">{settings.storeName}</div>
          <div className="text-xs">{settings.storeAddress}</div>
          <div className="text-xs">Tel: {settings.storePhone}</div>
        </div>

        {/* Transaction Info */}
        <div className="text-center border-b pb-2">
          <div className="text-xs">#{transaction.id.toString().padStart(3, '0')}</div>
          <div className="text-xs">{formattedDate} {formattedTime} WIB</div>
        </div>

        {/* Items */}
        <div className="space-y-1">
          {transaction.items.map((item, index) => (
            <div key={index}>
              <div className="flex justify-between">
                <span className="truncate">{item.name}</span>
                <span>{formatCurrency(item.price * item.quantity)}</span>
              </div>
              <div className="text-xs text-gray-600 ml-2">
                {item.quantity} x {formatCurrency(item.price)}
              </div>
            </div>
          ))}
        </div>

        {/* Totals */}
        <div className="border-t pt-2 space-y-1">
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>{formatCurrency(transaction.subtotal)}</span>
          </div>
          
          {transaction.tax > 0 && (
            <div className="flex justify-between">
              <span>Pajak:</span>
              <span>{formatCurrency(transaction.tax)}</span>
            </div>
          )}
          
          <div className="flex justify-between font-bold border-t pt-1">
            <span>Total:</span>
            <span>{formatCurrency(transaction.total)}</span>
          </div>
          
          <div className="flex justify-between">
            <span>Bayar:</span>
            <span>{formatCurrency(transaction.paymentAmount)}</span>
          </div>
          
          <div className="flex justify-between">
            <span>Kembalian:</span>
            <span>{formatCurrency(transaction.change)}</span>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center border-t pt-2">
          <div className="text-xs">Terima kasih atas kunjungan Anda!</div>
          <div className="text-xs">Barang yang sudah dibeli tidak dapat dikembalikan</div>
        </div>
      </CardContent>
    </Card>
  );
}
