import { ScanBarcode, Package, TrendingUp, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface BottomNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function BottomNavigation({ activeTab, setActiveTab }: BottomNavigationProps) {
  const tabs = [
    { id: "sales", label: "Kasir", icon: ScanBarcode },
    { id: "inventory", label: "Inventori", icon: Package },
    { id: "reports", label: "Laporan", icon: TrendingUp },
    { id: "settings", label: "Pengaturan", icon: Settings },
  ];

  return (
    <nav className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-surface dark:bg-card-dark border-t border-gray-200 dark:border-gray-700 safe-area">
      <div className="flex justify-around">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <Button
              key={tab.id}
              variant="ghost"
              onClick={() => setActiveTab(tab.id)}
              className={cn(
                "flex-1 py-3 flex flex-col items-center space-y-1 h-auto",
                activeTab === tab.id
                  ? "text-primary"
                  : "text-gray-500 dark:text-gray-400"
              )}
            >
              <Icon className="h-5 w-5" />
              <span className="text-xs">{tab.label}</span>
            </Button>
          );
        })}
      </div>
    </nav>
  );
}
