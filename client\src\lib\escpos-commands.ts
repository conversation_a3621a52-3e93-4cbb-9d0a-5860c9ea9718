export function generateESCPOSCommands(text: string): Uint8Array {
  const commands: number[] = [];
  
  // Initialize printer
  commands.push(0x1B, 0x40); // ESC @
  
  // Set character set to Indonesian
  commands.push(0x1B, 0x74, 0x12); // ESC t 18
  
  // Convert text to bytes
  const encoder = new TextEncoder();
  const textBytes = encoder.encode(text);
  
  // Add text
  commands.push(...Array.from(textBytes));
  
  // Add line feeds
  commands.push(0x0A, 0x0A, 0x0A);
  
  // Cut paper (if supported)
  commands.push(0x1D, 0x56, 0x00); // GS V 0
  
  return new Uint8Array(commands);
}

export function createReceiptCommands(receipt: {
  header: string;
  items: Array<{ name: string; quantity: number; price: number }>;
  subtotal: number;
  tax: number;
  total: number;
  payment: number;
  change: number;
  footer: string;
}): Uint8Array {
  const commands: number[] = [];
  
  // Initialize
  commands.push(0x1B, 0x40); // ESC @
  
  // Set alignment center
  commands.push(0x1B, 0x61, 0x01); // ESC a 1
  
  // Header
  const headerBytes = new TextEncoder().encode(receipt.header + "\n");
  commands.push(...Array.from(headerBytes));
  
  // Set alignment left
  commands.push(0x1B, 0x61, 0x00); // ESC a 0
  
  // Items
  receipt.items.forEach(item => {
    const itemLine = `${item.name} ${item.quantity}x${item.price}\n`;
    const itemBytes = new TextEncoder().encode(itemLine);
    commands.push(...Array.from(itemBytes));
  });
  
  // Separator
  commands.push(...Array.from(new TextEncoder().encode("--------------------------------\n")));
  
  // Totals
  const totalsText = `
Subtotal: ${receipt.subtotal}
Tax: ${receipt.tax}
Total: ${receipt.total}
Payment: ${receipt.payment}
Change: ${receipt.change}
`;
  
  const totalsBytes = new TextEncoder().encode(totalsText);
  commands.push(...Array.from(totalsBytes));
  
  // Footer
  commands.push(0x1B, 0x61, 0x01); // ESC a 1 (center)
  const footerBytes = new TextEncoder().encode(receipt.footer + "\n");
  commands.push(...Array.from(footerBytes));
  
  // Cut paper
  commands.push(0x1D, 0x56, 0x00); // GS V 0
  
  return new Uint8Array(commands);
}
